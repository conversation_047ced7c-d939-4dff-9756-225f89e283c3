# KMM Coroutine Memory Leak Fixes

## Overview

This document outlines the comprehensive solution for fixing Kotlin Multiplatform (KMM) coroutine-related memory leaks in the iOS app, specifically in the ProductDetail package.

## Root Causes Identified

1. **SkieSwiftFlow Consumption Issues**: Kotlin StateFlow/Flow not properly cancelled when Swift side is deallocated
2. **Task.detached with Insufficient Cancellation**: Underlying Kotlin coroutines remain active after Swift tasks are cancelled
3. **Strong Reference Cycles**: Domain store connectors holding strong references to services
4. **Shared DI Container Instances**: Services persisting beyond view lifecycle
5. **Missing Lifecycle Management**: No proper cleanup when views disappear or app goes to background

## Solutions Implemented

### 1. KMMFlowWrapper - Lifecycle-Aware Flow Management

**File**: `Packages/AppCore/Sources/AppCore/KMM/KMMFlowWrapper.swift`

**Key Features**:
- Proper cancellation of SkieSwiftFlow
- TaskCancellationHandler for cleanup
- Weak self references to prevent retain cycles
- Automatic cleanup on deinit

**Usage**:
```swift
let wrapper = KMMFlowWrapper<ResponseType>()
wrapper.observe(
    flow: kotlin<PERSON>low,
    onValue: { value in /* handle value */ },
    onError: { error in /* handle error */ }
)
// Wrapper automatically cancels on deinit or explicit cancel()
```

### 2. Enhanced ProductDetailService

**File**: `Packages/ProductDetail/Sources/ProductDetail/Domain/ProductDetailService.swift`

**Changes**:
- Replaced `Task.detached` with `KMMFlowWrapper`
- Added proper cleanup in deinit
- Eliminated retain cycles with weak references

### 3. Improved Domain Store Connector

**File**: `Packages/ProductDetail/Sources/ProductDetail/Domain/ProductDetailStore.swift`

**Changes**:
- Used weak references to service to prevent retain cycles
- Added Task cancellation checks
- Proper cleanup of async streams

### 4. Lifecycle-Aware SwiftUI Components

**File**: `Packages/AppCore/Sources/AppCore/SwiftUI/LifecycleAwareView.swift`

**Features**:
- SwiftUI view modifier for lifecycle management
- Automatic coroutine pause/resume on app background/foreground
- Scene phase change handling

### 5. DI Container Improvements

**File**: `Packages/ProductDetail/Sources/ProductDetail/DI/ProductDetailContainer.swift`

**Changes**:
- Changed domain store from `.shared` to `.unique` to prevent memory leaks
- Ensures proper cleanup when views are deallocated

### 6. KMM Coroutine Debugger

**File**: `Packages/AppCore/Sources/AppCore/KMM/KMMCoroutineDebugger.swift`

**Features**:
- Track active KMM flows
- Memory usage monitoring
- Debug logging for coroutine lifecycle
- Performance monitoring in debug builds

## Implementation Steps

### Step 1: Update ProductDetailView

Replace the existing lifecycle methods:

```swift
.lifecycleAware(
    onAppear: {
        // Initialize coroutines
    },
    onDisappear: {
        // Cleanup coroutines
        Task {
            await viewStore.dispatch(.cleanup)
        }
    },
    onScenePhaseChange: { phase in
        switch phase {
        case .background:
            Task { await viewStore.dispatch(.pause) }
        case .active:
            Task { await viewStore.dispatch(.resume) }
        default:
            break
        }
    }
)
```

### Step 2: Update Service Implementations

Replace direct SkieSwiftFlow consumption with KMMFlowWrapper:

```swift
// Before
Task.detached { [weak self] in
    for await element in kotlinFlow {
        // Handle element
    }
}

// After
let wrapper = KMMFlowWrapper<ResponseType>()
wrapper.observe(
    flow: kotlinFlow,
    onValue: { value in /* handle */ },
    onError: { error in /* handle */ }
)
```

### Step 3: Add Lifecycle Events

Add cleanup, pause, and resume events to your ViewStore events:

```swift
enum Event: OGViewEvent {
    // ... existing events
    case cleanup
    case pause
    case resume
}
```

### Step 4: Enable Debug Monitoring

In debug builds, enable memory monitoring:

```swift
#if DEBUG
KMMCoroutineDebugger.shared.startMemoryMonitoring()
#endif
```

## Testing and Validation

### Memory Leak Testing

1. **Instruments Testing**:
   - Use Xcode Instruments to monitor memory usage
   - Navigate to ProductDetail screens multiple times
   - Verify Kotlin objects are properly deallocated

2. **Unit Tests**:
   - Run `KMMMemoryLeakTests` to validate proper cleanup
   - Test service deallocation and flow cancellation

3. **Debug Monitoring**:
   - Use `KMMCoroutineDebugger.shared.printActiveFlowsSummary()` to check active flows
   - Monitor logs for proper flow lifecycle events

### Performance Testing

1. **Rapid Navigation Test**:
   - Navigate to/from ProductDetail screens rapidly
   - Verify no memory accumulation
   - Check that active flow count remains reasonable

2. **Background/Foreground Test**:
   - Put app in background during ProductDetail loading
   - Verify coroutines are paused
   - Check proper resumption when returning to foreground

## Best Practices

### 1. Always Use KMMFlowWrapper

Never consume SkieSwiftFlow directly. Always use KMMFlowWrapper for proper lifecycle management.

### 2. Implement Lifecycle Events

Add cleanup, pause, and resume events to all ViewStores that interact with KMM coroutines.

### 3. Use Weak References

Always use weak references when capturing services or stores in async contexts.

### 4. Monitor Memory Usage

Regularly check active flows using the debugger, especially during development.

### 5. Test Memory Leaks

Include memory leak tests in your test suite and run them regularly.

## Migration Checklist

- [ ] Replace direct SkieSwiftFlow usage with KMMFlowWrapper
- [ ] Update ProductDetailService with new architecture
- [ ] Add lifecycle events to ViewStore
- [ ] Update SwiftUI views with lifecycleAware modifier
- [ ] Change DI container from shared to unique instances
- [ ] Add KMM coroutine debugging
- [ ] Write and run memory leak tests
- [ ] Validate with Instruments
- [ ] Monitor production metrics

## Monitoring and Maintenance

### Production Monitoring

1. **Crash Analytics**: Monitor for memory-related crashes
2. **Performance Metrics**: Track app memory usage patterns
3. **User Experience**: Monitor for performance degradation

### Regular Maintenance

1. **Code Reviews**: Ensure new KMM integrations follow these patterns
2. **Testing**: Run memory leak tests with each release
3. **Monitoring**: Regular checks of active flow counts in debug builds

## Conclusion

These fixes address the root causes of KMM coroutine memory leaks by:

1. Providing proper lifecycle management for Kotlin StateFlow/Flow
2. Eliminating retain cycles between Swift and Kotlin objects
3. Adding comprehensive debugging and monitoring capabilities
4. Ensuring proper cleanup when views disappear or app backgrounds

The solution is backward-compatible and can be gradually rolled out across the codebase.
