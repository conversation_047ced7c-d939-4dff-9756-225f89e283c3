import AsyncAlgorithms
import Combine
import Foundation
import NativeAPI
import OGAppKitSDK
import OGCore
import OGR<PERSON>er

typealias OGProductDetailComponent = Skie.com_ottogroup_ogappkit_nativeui__api.ProductDetailComponent.__Sealed
typealias OGNativeApiTenant = __Bridge__OGNativeConfig_NativeApiTenant
typealias OGProductDetailScreen = SkieSwiftFlow<OGResult<ProductDetailScreen>>

// MARK: - ProductDetailServing

protocol ProductDetailServing {
  func fetchProductDetail(productId: String, secondaryId: String?, componentConfigsJson: String) async
  func fetchProductDetailScreen(for url: URL, componentConfigsJson: String) async
  func addProductToBasket(id: String) async throws -> Int
  func addVoucherToBasket(id: String, customName: String?) async throws -> Int
  func addProductToWishlist(id: String) async throws -> [String]
  func refreshWishlistCount() async throws -> [String]
  func refreshBasketCount() async throws -> Int
  func removeProductFromWishlist(id: String) async throws -> [String]
  func updateProductColorSelection(productId: String, screenId: String) async
  func updateProductVariantSelection(productId: String, screenId: String) async
  var successStream: AsyncChannel<(components: [OGProductDetailComponent], screenId: String)> { get async }
  var errorStream: AsyncChannel<Error> { get async }
}

// MARK: - ProductDetailService

actor ProductDetailService: ProductDetailServing {
  let successStream: AsyncChannel<(components: [OGProductDetailComponent], screenId: String)> = .init()
  let errorStream: AsyncChannel<Error> = .init()
  private let ogNative: OGNative
  private let logger: any OGLoggingDistributable
  private var productIdFlowWrapper: KMMFlowWrapper<ProductDetailScreen>?
  private var productRouteFlowWrapper: KMMFlowWrapper<ProductDetailScreen>?

  init(
    ogNative: OGNative = NativeAPIContainer.shared.nativeAPI(),
    logger: any OGLoggingDistributable = OGCoreContainer.shared.logger()
  ) {
    self.ogNative = ogNative
    self.logger = logger
  }

  func fetchProductDetail(productId: String, secondaryId: String?, componentConfigsJson: String) async {
    fetchProductDetailScreen(productId: productId, secondaryId: secondaryId, componentConfigsJson: componentConfigsJson)
  }

  func fetchProductDetailScreen(for url: URL, componentConfigsJson: String) async {
    fetchProductDetailScreen(url: url, componentConfigsJson: componentConfigsJson)
  }

  func updateProductColorSelection(productId: String, screenId: String) async {
    let context = ProductDetailScreenRequestContextFromColorSelection()
    updateProductDetailScreen(screenId: screenId, productId: productId, context: context)
  }

  func updateProductVariantSelection(productId: String, screenId: String) async {
    let context = ProductDetailScreenRequestContextFromVariantSelection()
    updateProductDetailScreen(screenId: screenId, productId: productId, context: context)
  }

  func addVoucherToBasket(id: String, customName: String?) async throws -> Int {
    let result = try await ogNative.addVoucherToBasket(id: id, customName: customName)
    return try basket(result: result, id: id)
  }

  func addProductToBasket(id: String) async throws -> Int {
    let result = try await ogNative.addProductToBasket(id: id)
    return try basket(result: result, id: id)
  }

  func addProductToWishlist(id: String) async throws -> [String] {
    let result = try await ogNative.addProductToWishlist(id: id)
    switch onEnum(of: result) {
    case let .failure(error):
      logger.log(.critical, domain: .service, message: error.failure.asError().localizedDescription)
      throw WishlistError.addToWishlistFailed(id)
    case let .success(wishlist):
      return wishlist.value.items.map {
        if let productId = $0.productId {
          productId
        } else {
          $0.id
        }
      }
    }
  }

  func removeProductFromWishlist(id: String) async throws -> [String] {
    let result = try await ogNative.removeProductFromWishlist(id: id)
    switch onEnum(of: result) {
    case let .failure(error):
      logger.log(.critical, domain: .service, message: error.failure.asError().localizedDescription)
      throw WishlistError.removeFromWishlistFailed(id)
    case let .success(wishlist):
      return wishlist.value.items.map {
        if let productId = $0.productId {
          productId
        } else {
          $0.id
        }
      }
    }
  }

  private func basket(result: OGResult<Basket>, id: String) throws -> Int {
    switch onEnum(of: result) {
    case let .failure(error):
      logger.log(.critical, domain: .service, message: error.failure.asError().localizedDescription)
      if error.failure is OGAppKitSDK.BasketError.Generic {
        throw BasketError.generic(id)
      }
      if error.failure is OGAppKitSDK.BasketError.GiftCardAlreadyInBasket {
        throw BasketError.giftCardAlreadyInBasket
      }
      if error.failure is OGAppKitSDK.BasketError.GiftCardNameTooLong {
        throw BasketError.giftCardNameTooLong
      }
      if error.failure is OGAppKitSDK.BasketError.GiftCardProductAlreadyInBasket {
        throw BasketError.giftCardProductAlreadyInBasket
      }
      if error.failure is OGAppKitSDK.BasketError.GiftCardSameValueAlreadyInBasket {
        throw BasketError.giftCardSameValueAlreadyInBasket
      }
      if error.failure is OGAppKitSDK.BasketError.ItemCountExceeded {
        throw BasketError.itemCountExceeded
      }
      if error.failure is OGAppKitSDK.BasketError.ProductUnavailable {
        throw BasketError.productUnavailable
      }

      throw BasketError.failed(id)

    case let .success(basket):
      return Int(basket.value.items.reduce(0) { $0 + $1.amount })
    }
  }

  private func fetchProductDetailScreen(url: URL, componentConfigsJson: String) {
    productRouteFlowWrapper?.cancel()
    productIdFlowWrapper?.cancel()

    let screen = ogNative.getProductDetailScreenByUrl(
      url: url.absoluteString,
      componentConfigsJson: componentConfigsJson
    )

    productRouteFlowWrapper = KMMFlowWrapper<ProductDetailScreen>()
    productRouteFlowWrapper?.observe(
      flow: screen,
      onValue: { [weak self] screenValue in
        Task {
          await self?.handleScreenSuccess(screenValue)
        }
      },
      onError: { [weak self] error in
        Task {
          await self?.errorStream.send(error)
        }
      }
    )
  }

  private func updateProductDetailScreen(screenId: String, productId: String, context: ProductDetailScreenRequestContext) {
    ogNative.updateProductDetailScreen(screenId: screenId, productId: productId, context: context)
  }

  private func fetchProductDetailScreen(productId: String, secondaryId: String?, componentConfigsJson: String) {
    productRouteFlowWrapper?.cancel()
    productIdFlowWrapper?.cancel()

    let screen = ogNative.getProductDetailScreen(
      id: productId,
      secondaryId: secondaryId,
      componentConfigsJson: componentConfigsJson
    )

    productIdFlowWrapper = KMMFlowWrapper<ProductDetailScreen>()
    productIdFlowWrapper?.observe(
      flow: screen,
      onValue: { [weak self] screenValue in
        Task {
          await self?.handleScreenSuccess(screenValue)
        }
      },
      onError: { [weak self] error in
        Task {
          await self?.errorStream.send(error)
        }
      }
    )
  }

  func refreshWishlistCount() async throws -> [String] {
    let result = try await ogNative.getWishlist()
    switch onEnum(of: result) {
    case let .failure(error):
      throw ProductDetailError.unknown(error.failure.asError())
    case let .success(wishlist):
      return wishlist.value.items.map {
        if let productId = $0.productId {
          productId
        } else {
          $0.id
        }
      }
    }
  }

  func refreshBasketCount() async throws -> Int {
    let result = try await ogNative.getBasket()
    switch onEnum(of: result) {
    case let .failure(error):
      throw ProductDetailError.unknown(error.failure.asError())
    case let .success(basket):
      return basket.value.items.count
    }
  }

  private func handleScreenSuccess(_ screen: ProductDetailScreen) async {
    let newComponents = screen.components.map { onEnum(of: $0) }
    await successStream.send((newComponents, screen.screenId))
  }

  deinit {
    productRouteFlowWrapper?.cancel()
    productIdFlowWrapper?.cancel()
    successStream.finish()
    errorStream.finish()
  }
}
