import Foundation
import OGAppKitSDK
import AsyncAlgorithms

/// A lifecycle-aware wrapper for KMM StateFlow/Flow consumption that ensures proper cancellation
public final class KMMFlowWrapper<T: AnyObject> {
    private var observationTask: Task<Void, Never>?
    private var isActive = false
    
    public init() {}
    
    /// Observes a SkieSwiftFlow with proper lifecycle management
    /// - Parameters:
    ///   - flow: The SkieSwiftFlow to observe
    ///   - onValue: Callback for successful values
    ///   - onError: Callback for errors
    /// - Returns: A cancellable task
    @discardableResult
    public func observe(
        flow: SkieSwiftFlow<OGResult<T>>,
        onValue: @escaping (T) -> Void,
        onError: @escaping (Error) -> Void
    ) -> Task<Void, Never> {
        // Cancel any existing observation
        cancel()
        
        isActive = true
        observationTask = Task.detached { [weak self] in
            // Use TaskLocal to track cancellation
            await withTaskCancellationHandler {
                for await element in flow {
                    guard let self = self, self.isActive else { 
                        break 
                    }
                    
                    // Check for task cancellation
                    if Task.isCancelled {
                        break
                    }
                    
                    do {
                        let value = try element.getOrThrow()
                        guard let typedValue = value as? T else { continue }
                        
                        // Dispatch to main actor if needed
                        await MainActor.run {
                            onValue(typedValue)
                        }
                    } catch {
                        await MainActor.run {
                            onError(error)
                        }
                    }
                }
            } onCancel: {
                // Explicit cleanup when cancelled
                Task { [weak self] in
                    await self?.cleanup()
                }
            }
        }
        
        return observationTask!
    }
    
    /// Cancels the current observation and cleans up resources
    public func cancel() {
        isActive = false
        observationTask?.cancel()
        observationTask = nil
    }
    
    private func cleanup() async {
        isActive = false
        // Additional cleanup can be added here for Kotlin-specific resources
    }
    
    deinit {
        cancel()
    }
}
